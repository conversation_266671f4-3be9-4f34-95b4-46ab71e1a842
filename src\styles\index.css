/* Real Estate Website - Dark Theme CSS Variables */
:root {
  /* Colors - Dark Theme Palette */
  --primary-bg: #0a0a0a;
  --secondary-bg: #1a1a1a;
  --tertiary-bg: #2a2a2a;
  --card-bg: #1e1e1e;
  --glass-bg: rgba(255, 255, 255, 0.05);

  /* Text Colors */
  --text-primary: #ffffff;
  --text-secondary: #b3b3b3;
  --text-muted: #808080;
  --text-accent: #f0f0f0;

  /* Accent Colors */
  --accent-primary: #00d4ff;
  --accent-secondary: #ff6b35;
  --accent-gradient: linear-gradient(135deg, #00d4ff 0%, #0099cc 100%);
  --accent-gradient-hover: linear-gradient(135deg, #00e6ff 0%, #00b3e6 100%);

  /* Status Colors */
  --success: #00ff88;
  --warning: #ffaa00;
  --error: #ff4444;

  /* Border Colors */
  --border-primary: #333333;
  --border-secondary: #404040;
  --border-accent: #00d4ff;

  /* Shadows */
  --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.3);
  --shadow-md: 0 4px 12px rgba(0, 0, 0, 0.4);
  --shadow-lg: 0 8px 24px rgba(0, 0, 0, 0.5);
  --shadow-xl: 0 16px 48px rgba(0, 0, 0, 0.6);
  --shadow-glow: 0 0 20px rgba(0, 212, 255, 0.3);

  /* Typography */
  --font-primary: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  --font-secondary: 'Poppins', 'Arial', sans-serif;
  --font-mono: 'Fira Code', 'Consolas', monospace;

  /* Font Sizes */
  --text-xs: 0.75rem;
  --text-sm: 0.875rem;
  --text-base: 1rem;
  --text-lg: 1.125rem;
  --text-xl: 1.25rem;
  --text-2xl: 1.5rem;
  --text-3xl: 1.875rem;
  --text-4xl: 2.25rem;
  --text-5xl: 3rem;
  --text-6xl: 3.75rem;

  /* Spacing */
  --space-1: 0.25rem;
  --space-2: 0.5rem;
  --space-3: 0.75rem;
  --space-4: 1rem;
  --space-5: 1.25rem;
  --space-6: 1.5rem;
  --space-8: 2rem;
  --space-10: 2.5rem;
  --space-12: 3rem;
  --space-16: 4rem;
  --space-20: 5rem;
  --space-24: 6rem;
  --space-32: 8rem;

  /* Border Radius */
  --radius-sm: 0.25rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  --radius-2xl: 1.5rem;
  --radius-full: 9999px;

  /* Transitions */
  --transition-fast: 0.15s ease-in-out;
  --transition-normal: 0.3s ease-in-out;
  --transition-slow: 0.5s ease-in-out;
  --transition-bounce: 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);

  /* Z-Index */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal: 1040;
  --z-popover: 1050;
  --z-tooltip: 1060;

  /* Container Sizes */
  --container-sm: 640px;
  --container-md: 768px;
  --container-lg: 1024px;
  --container-xl: 1280px;
  --container-2xl: 1536px;

  /* Animation Durations */
  --anim-fast: 0.2s;
  --anim-normal: 0.4s;
  --anim-slow: 0.8s;
  --anim-slower: 1.2s;
}

/* Global Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
  font-size: 16px;
}

body {
  font-family: var(--font-primary);
  background-color: var(--primary-bg);
  color: var(--text-primary);
  line-height: 1.6;
  overflow-x: hidden;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-secondary);
  font-weight: 600;
  line-height: 1.2;
  margin-bottom: var(--space-4);
}

h1 { font-size: var(--text-6xl); }
h2 { font-size: var(--text-5xl); }
h3 { font-size: var(--text-4xl); }
h4 { font-size: var(--text-3xl); }
h5 { font-size: var(--text-2xl); }
h6 { font-size: var(--text-xl); }

p {
  margin-bottom: var(--space-4);
  color: var(--text-secondary);
}

/* Links */
a {
  color: var(--accent-primary);
  text-decoration: none;
  transition: var(--transition-fast);
}

a:hover {
  color: var(--text-primary);
  text-shadow: var(--shadow-glow);
}

/* Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--space-3) var(--space-6);
  border: none;
  border-radius: var(--radius-lg);
  font-family: var(--font-primary);
  font-size: var(--text-base);
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition-normal);
  text-decoration: none;
  position: relative;
  overflow: hidden;
}

.btn-primary {
  background: var(--accent-gradient);
  color: var(--text-primary);
  box-shadow: var(--shadow-md);
}

.btn-primary:hover {
  background: var(--accent-gradient-hover);
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.btn-secondary {
  background: transparent;
  color: var(--accent-primary);
  border: 2px solid var(--accent-primary);
}

.btn-secondary:hover {
  background: var(--accent-primary);
  color: var(--primary-bg);
  box-shadow: var(--shadow-glow);
}

/* Container */
.container {
  max-width: var(--container-xl);
  margin: 0 auto;
  padding: 0 var(--space-4);
}

/* Utility Classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.flex { display: flex; }
.flex-col { flex-direction: column; }
.items-center { align-items: center; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }

.relative { position: relative; }
.absolute { position: absolute; }
.fixed { position: fixed; }

.w-full { width: 100%; }
.h-full { height: 100%; }
.min-h-screen { min-height: 100vh; }

/* Responsive Design */
/* Additional Animations */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Utility Animation Classes */
.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-pulse {
  animation: pulse 2s ease-in-out infinite;
}

.animate-slide-in-left {
  animation: slideInLeft 0.6s ease-out;
}

.animate-slide-in-right {
  animation: slideInRight 0.6s ease-out;
}

.animate-fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

.animate-scale-in {
  animation: scaleIn 0.6s ease-out;
}

/* Hover Effects */
.hover-lift {
  transition: var(--transition-normal);
}

.hover-lift:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-lg);
}

.hover-glow {
  transition: var(--transition-normal);
}

.hover-glow:hover {
  box-shadow: var(--shadow-glow);
}

/* Glass Morphism Effect */
.glass {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Gradient Text */
.gradient-text {
  background: var(--accent-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

@media (max-width: 768px) {
  :root {
    --text-6xl: 2.5rem;
    --text-5xl: 2rem;
    --text-4xl: 1.75rem;
    --space-32: 4rem;
    --space-24: 3rem;
    --space-20: 2.5rem;
  }

  .container {
    padding: 0 var(--space-3);
  }
}
